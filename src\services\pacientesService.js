import axios from '@/services/axios'

/**
 * Busca a lista simples de pacientes com id, nome e id_ficha
 * @returns {Promise<Array>} - Lista de pacientes com id, nome e id_ficha
 */
export async function getListaSimplesPacientes() {
    try {
        const response = await axios.get('/pacientes/lista-simples');

        if (!response || !response.data || !response.data.data)
            return [];

        return response.data.data;
    } catch (error) {
        console.error('Erro ao buscar lista simples de pacientes:', error);
        return [];
    }
}

export async function adicionarMeioContato(paciente_id, contato) {
    try {
        const response = await axios.post('/contatos-pacientes', {
            paciente_id,
            ...contato
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao salvar meio de contato:', error);
    }

    return false;
}

export async function excluirMeioContato(id) {
    try {
        const response = await axios.delete('/contatos-pacientes/' + id);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao excluir meio de contato:', error);
    }

    return false;
}

export async function salvarDiagnostico(paciente_id, diagnostico) {
    try {
        const response = await axios.post('/pacientes/salvar-diagnostico', {
            paciente_id,
            diagnostico,
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao salvar diagnóstico:', error);
    }

    return false;
}

export async function salvarPrognostico(paciente_id, prognostico) {
    try {
        const response = await axios.post('/pacientes/salvar-prognostico', {
            paciente_id,
            prognostico,
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return response;

    } catch (error) {
        console.error('Erro ao salvar diagnóstico:', error);
    }

    return false;
}

export async function getFichaInicial(paciente_id) {
    try {
        const response = await axios.get('/pacientes/ficha-inicial/' + paciente_id);

        if (!response || !response.data || !response.data.data || response.data.status !== 'success')
            return false

        return response.data.data

    } catch (error) {
        console.error('Erro ao consultar a ficha de avaliação inicial:', error);
    }

    return false
}

export async function sendWelcomeForm(paciente, questions) {
    try {
        const response = await axios.post('/pacientes/welcome-form', {
            id_paciente: paciente.id,
            questions: JSON.stringify(questions),
        });

        if (!response || !response.data || response.data.status !== 'success')
            return false

        return response

    } catch (error) {
        console.error('Erro ao enviar a ficha de avaliação inicial:', error);
    }

    return false
}

export async function getPacienteByToken(token) {
    try {
        const response = await axios.get('/pacientes/get-by-token/' + token);

        if (!response || !response.data || !response.data.paciente || response.data.status !== 'success')
            return false

        return response.data.paciente

    } catch (error) {
        console.error('Erro ao buscar paciente através do token da URL', error);
    }

    return false
}

export async function getFichaInicialByToken(token) {
    try {
        const response = await axios.get('/pacientes/ficha-inicial-by-token/' + token);

        if (!response || !response.data || response.data.status !== 'success')
            return false

        return response.data.data

    } catch (error) {
        console.error('Erro ao buscar ficha inicial através do token da URL', error);
    }

    return false
}

export async function addNovoPaciente(paciente) {
    try {
        const response = await axios.post('/pacientes', paciente);

        if (!response || response.status !== 200) {
            return false;
        }

        // Retornar os dados do paciente criado se disponível
        if (response.data && response.data.data) {
            return response.data.data;
        }

        // Fallback para compatibilidade
        return response.data || true;
    } catch (error) {
        console.error('Erro ao adicionar paciente:', error);
        return false;
    }
}

export async function updatePaciente(paciente) {
    const response = await axios.put('/pacientes/' + paciente.id, paciente)

    return (response && response.status == 200)
}

export async function updatePacienteField(pacienteId, field, value) {
    try {
        const data = {};
        data[field] = value;

        const response = await axios.patch('/pacientes/' + pacienteId, data);

        if (!response || response.status !== 200) {
            return false;
        }

        return response.data;
    } catch (error) {
        console.error('Erro ao atualizar campo do paciente:', error);
        return false;
    }
}

export async function excluirPaciente(id) {
    try {
        const response = await axios.delete('/pacientes/' + id);

        if (!response || !response.data || response.data.status !== 'success')
            return false;

        return true;

    } catch (error) {
        console.error('Erro ao excluir paciente:', error);
        return false;
    }
}

export async function getPaciente(id, clinicaSlug = null) {
    let url;
    if (clinicaSlug) {
        // Usar rota com slug da clínica para acesso multi-tenancy
        url = `/${clinicaSlug}/pacientes/${id}`;
    } else {
        // Usar rota padrão
        url = `/pacientes/${id}`;
    }

    const paciente = await axios.get(url)

    if (!paciente || !paciente.data)
        return null

    return paciente.data
}

export async function getClinicaPacientes(clinicaSlug) {
    try {
        const response = await axios.get(`/${clinicaSlug}/pacientes`)

        if (!response || !response.data)
            return [];

        return response.data

    } catch (error) {
        console.error('Erro ao consultar pacientes da clínica:', error);
    }

    return []
}

export async function searchPacientes(search = '') {
    const allPacientes = await axios.post('/pacientes/search', {
        search: search
    });

    if (!allPacientes || !allPacientes.data)
        return [];

    return allPacientes.data;
}

export async function uploadImage(image, type, date, description) {
    let data = new FormData();
    data.append('image', image);
    data.append('type', type);
    data.append('date', date);
    data.append('description', description);

    await axios.post('/pacientes/upload-image', data,
        {
            header: { 'Content-Type': 'image/png' }
        })
}