<template>
  <div class="page-header align-items-start min-vh-100" v-bind:style="backgroundImage" :class="{ 'breathing': showAnimations }">
    <!-- Background animado -->
    <span class="mask bg-gradient-dark opacity-6" :class="{ 'animated-mask': showAnimations }"></span>

    <!-- Loading overlay -->
    <div
      v-if="isProcessing"
      class="loading-overlay"
      :class="{ 'show': isProcessing }"
    ></div>

    <div class="container my-auto" :class="{ 'animated-container': showAnimations }">
      <div class="row">
        <div class="col-lg-4 col-md-8 col-12 mx-auto">
          <div class="card z-index-0 reset-card" :class="{ 'processing': isProcessing, 'animated-card': showAnimations }">
            <!-- Header do card com logo -->
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2" :class="{ 'animated-header': showAnimations }">
              <div
                class="shadow-secondary border-radius-lg py-3 bg-gradient-lumi"
                style="border: 1px solid #d2d2d2;"
              >
                <img :src="LumiBlueLogo" class="reset-page-logo" :class="{ 'animated-logo': showAnimations }" />
              </div>
            </div>

            <!-- Corpo do card -->
            <div class="card-body" :class="{ 'animated-body': showAnimations }">
              <!-- Estado: Validando token -->
              <div v-if="validandoToken" class="text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <h5>Validando link...</h5>
                <p class="text-muted">Aguarde enquanto verificamos seu link de redefinição.</p>
              </div>

              <!-- Estado: Token inválido -->
              <div v-else-if="tokenInvalido" class="text-center py-4">
                <div class="error-icon mb-3">
                  <font-awesome-icon :icon="['fas', 'exclamation-triangle']" />
                </div>
                <h5 class="text-danger">Link Inválido</h5>
                <p class="text-muted mb-4">{{ mensagemErro }}</p>
                <material-button
                  variant="outline"
                  color="primary"
                  @click="voltarParaLogin"
                >
                  Voltar para Login
                </material-button>
              </div>

              <!-- Estado: Formulário de nova senha -->
              <div v-else-if="tokenValido" class="reset-form">
                <div class="text-center mb-4">
                  <h5 class="reset-title">Redefinir Senha</h5>
                  <p class="reset-subtitle">Digite sua nova senha abaixo</p>
                  <p class="user-email">{{ email }}</p>
                </div>

                <form @submit.prevent="redefinirSenha" class="text-start">
                  <!-- Campo nova senha -->
                  <div class="mb-3 input-with-icon" :class="{ 'animated-field': showAnimations }">
                    <div class="input-icon-wrapper">
                      <i class="fas fa-lock input-icon"></i>
                      <MaterialInput
                        id="nova-senha"
                        :type="showPassword ? 'text' : 'password'"
                        placeholder="Nova senha"
                        v-model="novaSenha"
                        name="nova-senha"
                        class="input-with-left-icon input-with-right-icon"
                        :class="{ 'is-invalid': erroNovaSenha }"
                        required
                      />
                      <button
                        type="button"
                        class="password-toggle-btn"
                        @click="togglePasswordVisibility"
                        tabindex="-1"
                      >
                        <i :class="showPassword ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
                      </button>
                    </div>
                    <div v-if="erroNovaSenha" class="invalid-feedback">
                      {{ erroNovaSenha }}
                    </div>
                  </div>

                  <!-- Campo confirmar senha -->
                  <div class="mb-3 input-with-icon" :class="{ 'animated-field': showAnimations }">
                    <div class="input-icon-wrapper">
                      <i class="fas fa-lock input-icon"></i>
                      <MaterialInput
                        id="confirmar-senha"
                        :type="showConfirmPassword ? 'text' : 'password'"
                        placeholder="Confirmar nova senha"
                        v-model="confirmarSenha"
                        name="confirmar-senha"
                        class="input-with-left-icon input-with-right-icon"
                        :class="{ 'is-invalid': erroConfirmarSenha }"
                        required
                      />
                      <button
                        type="button"
                        class="password-toggle-btn"
                        @click="toggleConfirmPasswordVisibility"
                        tabindex="-1"
                      >
                        <i :class="showConfirmPassword ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
                      </button>
                    </div>
                    <div v-if="erroConfirmarSenha" class="invalid-feedback">
                      {{ erroConfirmarSenha }}
                    </div>
                  </div>

                  <!-- Indicador de força da senha -->
                  <div v-if="novaSenha" class="password-strength mb-3">
                    <div class="strength-label">Força da senha:</div>
                    <div class="strength-bar">
                      <div 
                        class="strength-fill" 
                        :class="passwordStrengthClass"
                        :style="{ width: passwordStrengthPercentage + '%' }"
                      ></div>
                    </div>
                    <div class="strength-text" :class="passwordStrengthClass">
                      {{ passwordStrengthText }}
                    </div>
                  </div>

                  <!-- Botão redefinir -->
                  <div class="text-center">
                    <material-button
                      class="my-4 mb-2"
                      variant="gradient"
                      color="secondary"
                      fullWidth
                      :loading="redefinindo"
                      :loadingText="'Redefinindo...'"
                    >
                      Redefinir Senha
                    </material-button>
                  </div>
                </form>
              </div>

              <!-- Estado: Sucesso -->
              <div v-else-if="senhaRedefinida" class="text-center py-4">
                <div class="success-icon mb-3">
                  <font-awesome-icon :icon="['fas', 'check-circle']" />
                </div>
                <h5 class="text-success">Senha Redefinida!</h5>
                <p class="text-muted mb-4">Sua senha foi redefinida com sucesso. Você já pode fazer login.</p>
                <material-button
                  variant="gradient"
                  color="secondary"
                  @click="voltarParaLogin"
                >
                  Fazer Login
                </material-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="footer position-absolute bottom-2 py-2 w-100" :class="{ 'animated-footer': showAnimations }">
      <div class="container">
        <div class="row align-items-center justify-content-lg-between">
          <div class="col-12 my-auto">
            <div
              class="copyright text-center text-sm text-white text-lg-start d-flex flex-column"
              style="font-weight: 400"
            >
              <span style="font-size: 11pt"
                >© {{ new Date().getFullYear() }} Lumi Plan</span
              >
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
/* ===== ESTILOS BASE (COPIADOS DO ENTRAR.VUE) ===== */

/* Background e animações */
.page-header {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.page-header.breathing::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  animation: breathe 8s ease-in-out infinite;
  z-index: -1;
}

@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.animated-mask {
  animation: fadeIn 1.2s ease-out;
}

/* Container e card */
.container {
  position: relative;
  z-index: 2;
}

.animated-container {
  animation: slideInUp 0.8s ease-out 0.2s both;
}

.reset-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.animated-card {
  animation: cardEntrance 0.6s ease-out 0.4s both;
}

/* Header do card */
.animated-header {
  animation: headerSlide 0.5s ease-out 0.6s both;
}

.bg-gradient-lumi {
  background: linear-gradient(135deg, #56809F 0%, #4a6d85 100%);
}

/* Logo */
.reset-page-logo {
  max-width: 150px;
  height: auto;
  display: block;
  margin: 0 auto;
  filter: brightness(0) invert(1);
}

.animated-logo {
  animation: logoFloat 0.6s ease-out 0.8s both;
}

/* Body do card */
.animated-body {
  animation: bodyFadeIn 0.5s ease-out 1s both;
}

/* Campos de input */
.input-with-icon {
  position: relative;
  margin-bottom: 1rem;
}

.input-icon-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  z-index: 3;
  font-size: 0.9rem;
}

.input-with-left-icon {
  padding-left: 45px !important;
}

.input-with-right-icon {
  padding-right: 45px !important;
}

.password-toggle-btn {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  z-index: 3;
  padding: 5px;
  border-radius: 3px;
  transition: color 0.3s ease;
}

.password-toggle-btn:hover {
  color: #56809F;
}

.animated-field {
  animation: fieldSlideIn 0.4s ease-out both;
}

/* Footer */
.animated-footer {
  animation: footerFadeIn 0.5s ease-out 1.4s both;
}

/* Animações */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes headerSlide {
  from {
    opacity: 0;
    transform: translateY(-15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes logoFloat {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bodyFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fieldSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes footerFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== ESTILOS ESPECÍFICOS PARA REDEFINIÇÃO DE SENHA ===== */
.reset-card {
  transition: all 0.3s ease;
}

.reset-card.processing {
  transform: scale(0.98);
  opacity: 0.9;
}

.reset-page-logo {
  max-width: 150px;
  height: auto;
  display: block;
  margin: 0 auto;
}

.reset-title {
  color: #2c3e50;
  font-weight: 600;
  margin: 0;
  font-size: 1.25rem;
}

.reset-subtitle {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0.5rem 0;
  line-height: 1.4;
}

.user-email {
  color: #56809F;
  font-weight: 500;
  font-size: 0.9rem;
  background-color: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border-left: 3px solid #56809F;
  margin: 0;
}

/* Ícones de estado */
.error-icon, .success-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-icon {
  color: #dc3545;
}

.success-icon {
  color: #28a745;
}

/* Indicador de força da senha */
.password-strength {
  margin-top: 0.5rem;
}

.strength-label {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.strength-bar {
  height: 4px;
  background-color: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  background-color: #dc3545;
}

.strength-fill.medium {
  background-color: #ffc107;
}

.strength-fill.strong {
  background-color: #28a745;
}

.strength-text {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.strength-text.weak {
  color: #dc3545;
}

.strength-text.medium {
  color: #ffc107;
}

.strength-text.strong {
  color: #28a745;
}

/* Loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.25);
  z-index: 9998;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out;
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
}

.loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Responsividade */
@media (max-width: 767.98px) {
  .reset-title {
    font-size: 1.1rem;
  }

  .reset-subtitle {
    font-size: 0.85rem;
  }

  .user-email {
    font-size: 0.85rem;
    padding: 0.4rem 0.8rem;
  }

  .error-icon, .success-icon {
    font-size: 2.5rem;
  }
}
</style>

<script>
import MaterialInput from "@/components/MaterialInput.vue";
import MaterialButton from "@/components/MaterialButton.vue";
import whiteConsultory from "@/assets/img/lumi/whiteConsultory.jpg";
import LumiBlueLogo from "@/assets/img/lumi/lumi-vision-logo-170.png";
import passwordResetService from "@/services/passwordResetService.js";
import router from "../router/index.js";
import cSwal from "@/utils/cSwal.js";

export default {
  name: "RedefinirSenha",
  components: {
    MaterialInput,
    MaterialButton,
  },
  data() {
    return {
      LumiBlueLogo,
      showAnimations: false,
      // Estados da página
      validandoToken: true,
      tokenValido: false,
      tokenInvalido: false,
      senhaRedefinida: false,
      isProcessing: false,
      // Dados do formulário
      token: '',
      email: '',
      novaSenha: '',
      confirmarSenha: '',
      redefinindo: false,
      // Controles de visibilidade
      showPassword: false,
      showConfirmPassword: false,
      // Erros
      mensagemErro: '',
      erroNovaSenha: '',
      erroConfirmarSenha: '',
    };
  },
  computed: {
    backgroundImage() {
      return {
        backgroundImage: `url(${whiteConsultory})`,
        transform: "scale(1.05)",
      };
    },
    passwordStrengthPercentage() {
      const strength = this.calculatePasswordStrength(this.novaSenha);
      return (strength / 4) * 100;
    },
    passwordStrengthClass() {
      const strength = this.calculatePasswordStrength(this.novaSenha);
      if (strength <= 1) return 'weak';
      if (strength <= 2) return 'medium';
      return 'strong';
    },
    passwordStrengthText() {
      const strength = this.calculatePasswordStrength(this.novaSenha);
      if (strength <= 1) return 'Fraca';
      if (strength <= 2) return 'Média';
      return 'Forte';
    }
  },
  async mounted() {
    // Obter parâmetros da URL
    this.token = this.$route.query.token;
    this.email = this.$route.query.email;

    if (!this.token || !this.email) {
      this.tokenInvalido = true;
      this.mensagemErro = 'Link de redefinição inválido. Parâmetros obrigatórios não encontrados.';
      this.validandoToken = false;
      return;
    }

    // Validar token
    await this.validarToken();

    // Iniciar animações após validação
    setTimeout(() => {
      this.showAnimations = true;
    }, 300);
  },
  methods: {
    async validarToken() {
      try {
        const result = await passwordResetService.validateResetToken(this.token, this.email);

        if (result.success) {
          this.tokenValido = true;
        } else {
          this.tokenInvalido = true;
          this.mensagemErro = result.error;
        }
      } catch (error) {
        console.error('Erro ao validar token:', error);
        this.tokenInvalido = true;
        this.mensagemErro = 'Erro interno. Tente novamente mais tarde.';
      } finally {
        this.validandoToken = false;
      }
    },

    togglePasswordVisibility() {
      this.showPassword = !this.showPassword;
    },

    toggleConfirmPasswordVisibility() {
      this.showConfirmPassword = !this.showConfirmPassword;
    },

    calculatePasswordStrength(password) {
      if (!password) return 0;

      let strength = 0;

      // Comprimento mínimo
      if (password.length >= 8) strength++;

      // Contém números
      if (/\d/.test(password)) strength++;

      // Contém letras minúsculas e maiúsculas
      if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++;

      // Contém caracteres especiais
      if (/[^A-Za-z0-9]/.test(password)) strength++;

      return strength;
    },

    validarFormulario() {
      this.erroNovaSenha = '';
      this.erroConfirmarSenha = '';

      let valido = true;

      // Validar nova senha
      if (!this.novaSenha) {
        this.erroNovaSenha = 'Por favor, digite sua nova senha';
        valido = false;
      } else if (this.novaSenha.length < 8) {
        this.erroNovaSenha = 'A senha deve ter pelo menos 8 caracteres';
        valido = false;
      }

      // Validar confirmação
      if (!this.confirmarSenha) {
        this.erroConfirmarSenha = 'Por favor, confirme sua nova senha';
        valido = false;
      } else if (this.novaSenha !== this.confirmarSenha) {
        this.erroConfirmarSenha = 'As senhas não coincidem';
        valido = false;
      }

      return valido;
    },

    async redefinirSenha() {
      if (this.redefinindo) return;

      if (!this.validarFormulario()) {
        return;
      }

      this.redefinindo = true;
      this.isProcessing = true;

      try {
        const result = await passwordResetService.resetPassword(
          this.token,
          this.email,
          this.novaSenha,
          this.confirmarSenha
        );

        if (result.success) {
          this.senhaRedefinida = true;
          this.tokenValido = false;

          cSwal.cSuccess('Senha redefinida com sucesso!');
        } else {
          if (result.errors) {
            // Tratar erros específicos de validação
            if (result.errors.password) {
              this.erroNovaSenha = result.errors.password[0];
            }
            if (result.errors.password_confirmation) {
              this.erroConfirmarSenha = result.errors.password_confirmation[0];
            }
          } else {
            cSwal.cError(result.error);
          }
        }
      } catch (error) {
        console.error('Erro ao redefinir senha:', error);
        cSwal.cError('Erro interno. Tente novamente mais tarde.');
      } finally {
        this.redefinindo = false;
        this.isProcessing = false;
      }
    },

    voltarParaLogin() {
      this.$router.push('/');
    }
  }
};
</script>
