# Fix: Token Blacklisted Error on Page Reload

## Problema Identificado

Quando a página é recarregada, o seguinte erro ocorre:
```
PATCH http://local2.api.lumi/profile 401 (Unauthorized)
"message": "The token has been blacklisted"
```

### Causa Raiz

1. Ao recarregar a página, o `router.beforeEach` chama `usuariosService.refreshAuth()`
2. O `refreshAuth()` chama `/auth/refresh` que invalida o token antigo (adiciona à blacklist) e gera um novo
3. Requisições em paralelo (como `updateProfile`) ainda usam o token antigo que acabou de ser blacklisted
4. O backend rejeita essas requisições com "The token has been blacklisted"

## Soluções Implementadas

### 1. Frontend: Sistema de Retry Automático (✅ Implementado)

**Arquivo modificado:** `src/services/axios.js`

Implementado um interceptor de resposta que:
- Detecta erros 401 com mensagem "blacklisted"
- Automaticamente tenta fazer refresh do token
- Coloca requisições falhadas em uma fila
- Reexecuta as requisições com o novo token após o refresh
- Evita múltiplos refreshes simultâneos

**Benefícios:**
- Transparente para o usuário
- Não requer mudanças em outros arquivos
- Resolve o problema de requisições paralelas

### 2. Backend: Correção do AuthController (✅ Implementado)

**Arquivo modificado:** `app/Http/Controllers/AuthController.php`

Corrigido o método `refresh()` para:
- Usar `auth()->payload()` em vez de `auth()->user()` antes do refresh
- Adicionar tratamento de exceção para `TokenBlacklistedException`
- Evitar chamadas que validam o token antes de fazer o refresh

**Benefícios:**
- Evita tentar validar um token que será blacklisted
- Tratamento adequado de erros
- Fluxo de refresh mais robusto

### 3. Backend: Correção dos Global Scopes (✅ Implementado)

**Arquivos modificados:**
- `app/Scopes/ClinicaScope.php`
- `app/Scopes/HardClinicaScope.php`

Modificações nos scopes para:
- Não aplicar o scope em rotas de autenticação (`auth/refresh`, `auth/login`, `auth/logout`)
- Adicionar tratamento de exceção para `TokenBlacklistedException`
- Evitar que queries de models tentem validar tokens blacklisted

**Benefícios:**
- Resolve o problema na raiz (ClinicaScope chamando `auth()->payload()`)
- Permite que o refresh funcione sem interferência dos scopes
- Mantém a segurança em outras rotas

### 4. Backend: Grace Period (⚠️ Recomendado - Configuração Opcional)

**Arquivo de configuração:** `config/jwt.php` (linha 235)

O JWT já está configurado para aceitar um grace period, mas está definido como 0 segundos:
```php
'blacklist_grace_period' => env('JWT_BLACKLIST_GRACE_PERIOD', 0),
```

**Para ativar o grace period (opcional):**

1. Adicione a seguinte linha ao arquivo `.env` do backend:
   ```
   JWT_BLACKLIST_GRACE_PERIOD=10
   ```

   Isso permite que tokens recém-blacklisted ainda sejam aceitos por 10 segundos, tempo suficiente para requisições paralelas completarem.

2. Reinicie o servidor Laravel após adicionar a variável de ambiente.

**Benefícios:**
- Camada adicional de proteção
- Permite requisições paralelas durante o período de graça
- Configuração padrão do tymon/jwt-auth para este cenário

## Resumo das Soluções

**Implementadas (✅):**
1. ✅ Frontend: Sistema de retry automático com fila de requisições
2. ✅ Backend: Correção do método `refresh()` no AuthController
3. ✅ Backend: Correção dos Global Scopes (ClinicaScope e HardClinicaScope)

**Opcional (⚠️):**
4. ⚠️ Backend: Grace period de 10 segundos (adicionar no `.env`)

Com as 3 soluções implementadas, o problema está resolvido. O grace period é uma camada adicional de segurança opcional.

## Testando a Solução

1. Faça login na aplicação
2. Recarregue a página (F5)
3. Observe que não há mais erros 401 de "token blacklisted"
4. Verifique no console que requisições são automaticamente retentadas se necessário
5. Teste fazer várias requisições simultâneas após recarregar a página

## Arquivos Modificados

### Frontend
- ✅ `src/services/axios.js` - Implementado retry automático e fila de requisições

### Backend
- ✅ `app/Http/Controllers/AuthController.php` - Corrigido método `refresh()`
- ✅ `app/Scopes/ClinicaScope.php` - Adicionado tratamento de exceção e skip em rotas de auth
- ✅ `app/Scopes/HardClinicaScope.php` - Adicionado tratamento de exceção e skip em rotas de auth
- ⚠️ `.env` (opcional) - Adicionar `JWT_BLACKLIST_GRACE_PERIOD=10`

## Detalhes Técnicos

### Problema Original

O erro ocorria porque:
1. Ao recarregar a página, o `router.beforeEach` chamava `refreshAuth()`
2. O `refreshAuth()` chamava `/auth/refresh` no backend
3. O método `refresh()` do AuthController chamava `auth()->user()->id`
4. Isso acionava o `HardClinicaScope` no model `User`
5. O scope chamava `auth()->payload()` para filtrar por clínica
6. Como o token estava prestes a ser blacklisted, isso causava a exceção

### Solução Implementada

1. **AuthController**: Mudou de `auth()->user()->id` para `auth()->payload()->get('sub')` para evitar carregar o model User
2. **Scopes**: Adicionaram verificação de rota e tratamento de exceção para não tentar validar tokens em rotas de autenticação
3. **Frontend**: Implementou retry automático para casos edge

### Por que funciona

- Os scopes agora não são aplicados em rotas de autenticação
- O AuthController não aciona mais os scopes durante o refresh
- O frontend tem uma camada de proteção adicional com retry automático
- Todas as requisições paralelas são enfileiradas e reexecutadas com o novo token

